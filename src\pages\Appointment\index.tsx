import QuickView from '@/components/ServiceDurationStatistics/QuickView';
import { OrderStatus } from '@/constant';
import { ComplaintStatus } from '@/constants/complaint';
import { DictionarieState } from '@/models/dictionarie';
import { getByOrder as getComplaintsByOrder } from '@/services/complaints';
import {
  adminApplyRefund,
  adminCancel,
  adminDelete,
  cancel,
  complete,
  deliver,
  refund,
  remove,
  start,
  transfer,
} from '@/services/order';
import { getOrderDurationStatistics } from '@/services/service-duration-statistics';
import {
  DownOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { ProColumns } from '@ant-design/pro-components';
import { connect, useModel } from '@umijs/max';
import type { MenuProps } from 'antd';
import {
  Button,
  Card,
  Dropdown,
  message,
  Modal,
  Popconfirm,
  Space,
  Tabs,
  Tag,
  Tooltip,
} from 'antd';
import React, { useRef, useState } from 'react';
import OrderComplaintModal from '../Complaint/components/OrderComplaintModal';
import AcceptModal from './AcceptModal';
import AdminActionModal from './AdminActionModal';
import { StatusBoardView, TimelineView } from './components';
import OrderComplaintListModal from './components/OrderComplaintListModal';
import DetailList from './DetailList';
import DetailModal from './DetailModal';
import OrderLog from './OrderLog';
import RefundAuditModal from './RefundAuditModal';
import ReviewDetailModal from './ReviewDetailModal';
import ServicePhotosModal from './ServicePhotosModal';
import SpecialNotesModal from './SpecialNotesModal';
import UpdateAddressModal from './UpdateAddressModal';
// import EditModal from './EditModal';

const Appointment: React.FC<{ dictionarie: DictionarieState }> = ({
  dictionarie,
}) => {
  const { initialState } = useModel('@@initialState');
  const [modalVisible, setModalVisible] = useState(false);

  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showLog, setShowLog] = useState<boolean>(false);
  const [current, setCurrent] = useState<API.Order | undefined>(undefined);

  // 当前激活的tab
  const [activeTab, setActiveTab] = useState('board');

  // 各个组件的刷新函数引用
  const statusBoardRef = useRef<{ refresh: () => void } | null>(null);
  const timelineRef = useRef<{ refresh: () => void } | null>(null);
  const detailListRef = useRef<{ refresh: () => void } | null>(null);

  // 添加审核弹窗状态
  const [refundAuditVisible, setRefundAuditVisible] = useState<boolean>(false);

  // 添加查看评价弹窗状态
  const [reviewDetailVisible, setReviewDetailVisible] =
    useState<boolean>(false);

  // 添加查看服务照片弹窗状态
  const [servicePhotosVisible, setServicePhotosVisible] =
    useState<boolean>(false);

  // 添加查看特殊情况说明弹窗状态
  const [specialNotesVisible, setSpecialNotesVisible] =
    useState<boolean>(false);

  // 添加修改地址弹窗状态
  const [updateAddressVisible, setUpdateAddressVisible] =
    useState<boolean>(false);

  // 添加管理员操作弹窗状态
  const [adminActionVisible, setAdminActionVisible] = useState<boolean>(false);
  const [adminActionType, setAdminActionType] = useState<
    'cancel' | 'refund' | 'delete'
  >('cancel');
  const [adminActionLoading, setAdminActionLoading] = useState<boolean>(false);

  // 添加投诉录入相关状态
  const [complaintVisible, setComplaintVisible] = useState<boolean>(false);

  // 添加投诉列表查看状态
  const [complaintListVisible, setComplaintListVisible] =
    useState<boolean>(false);

  // 添加订单投诉状态缓存
  const [orderComplaints, setOrderComplaints] = useState<
    Record<number, API.Complaint[]>
  >({});

  // 刷新当前激活的tab内容
  const handleRefreshCurrentTab = () => {
    switch (activeTab) {
      case 'board':
        statusBoardRef.current?.refresh();
        break;
      case 'timeline':
        timelineRef.current?.refresh();
        break;
      case 'table':
        console.log('刷新列表', detailListRef.current);
        detailListRef.current?.refresh();
        break;
      default:
        break;
    }
  };

  /** 检查订单是否有未处理的投诉 */
  const checkOrderComplaints = async (orderId: number) => {
    if (orderComplaints[orderId]) {
      return orderComplaints[orderId];
    }

    try {
      const response = await getComplaintsByOrder(orderId);
      if (!response.errCode && response.data?.list) {
        const complaints = response.data.list || [];
        setOrderComplaints((prev) => ({
          ...prev,
          [orderId]: complaints,
        }));
        return complaints;
      }
    } catch (error) {
      console.error('获取订单投诉失败:', error);
    }
    return [];
  };

  /** 获取订单未处理投诉数量 */
  const getUnhandledComplaintsCount = (orderId: number) => {
    const complaints = orderComplaints[orderId] || [];
    return complaints.filter(
      (complaint) =>
        complaint.status === ComplaintStatus.待处理 ||
        complaint.status === ComplaintStatus.处理中,
    ).length;
  };

  /** 派单 */
  const handleAccept = async (record: API.Order, employeeId: number) => {
    const { id, status } = record;
    let method;
    if (status === OrderStatus.待接单) {
      method = deliver;
    } else if (status === OrderStatus.待服务) {
      method = transfer;
    } else {
      message.error('订单状态错误');
      return;
    }
    const response = await method(id, employeeId);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('派单成功');
      setModalVisible(false);
      // 刷新列表
      handleRefreshCurrentTab();
    }
  };

  /** 开始服务 */
  const handleStart = async (record: API.Order, employeeId: number) => {
    const { id } = record;
    const response = await start(id, employeeId);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('修改成功');
      // 刷新列表
      handleRefreshCurrentTab();
    }
  };

  /** 完成服务 */
  const handleComplete = async (record: API.Order, employeeId: number) => {
    const { id } = record;
    const response = await complete(id, employeeId);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('修改成功');
      // 刷新列表
      handleRefreshCurrentTab();
    }
  };

  /** 退款 */
  const handleRefund = async (record: API.Order) => {
    const { sn } = record;
    const response = await refund(sn);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('退款成功');
      // 刷新列表
      handleRefreshCurrentTab();
    }
  };

  /** 退款审核 */
  const handleAuditRefund = async (record: API.Order) => {
    setCurrent(record);
    setRefundAuditVisible(true);
  };

  /** 查看评价 */
  const handleViewReview = (record: API.Order) => {
    setCurrent(record);
    setReviewDetailVisible(true);
  };

  /** 查看投诉列表 */
  const handleViewComplaints = (record: API.Order) => {
    setCurrent(record);
    setComplaintListVisible(true);
  };

  /** 查看服务照片 */
  const handleViewServicePhotos = (record: API.Order) => {
    setCurrent(record);
    setServicePhotosVisible(true);
  };

  /** 查看特殊情况说明 */
  const handleViewSpecialNotes = (record: API.Order) => {
    setCurrent(record);
    setSpecialNotesVisible(true);
  };

  /** 取消订单 */
  const handleCancel = async (record: API.Order) => {
    const { id } = record;
    const response = await cancel(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('取消成功');
      // 刷新列表
      handleRefreshCurrentTab();
    }
  };

  /** 删除订单 */
  const handleDel = async (record: API.Order) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      // 刷新列表
      handleRefreshCurrentTab();
    }
  };

  /** 管理员操作 - 打开确认弹窗 */
  const handleAdminAction = (
    record: API.Order,
    actionType: 'cancel' | 'refund' | 'delete',
  ) => {
    setCurrent(record);
    setAdminActionType(actionType);
    setAdminActionVisible(true);
  };

  /** 管理员操作 - 确认执行 */
  const handleAdminActionConfirm = async (reason?: string) => {
    if (!current || !initialState?.id) {
      message.error('操作失败：缺少必要信息');
      return;
    }

    setAdminActionLoading(true);
    try {
      const operatorId = initialState.id;
      let response;

      switch (adminActionType) {
        case 'cancel':
          response = await adminCancel(current.id, { operatorId, reason });
          break;
        case 'refund':
          response = await adminApplyRefund(current.sn, { operatorId, reason });
          break;
        case 'delete':
          response = await adminDelete(current.id, { operatorId, reason });
          break;
        default:
          message.error('未知操作类型');
          return;
      }

      if (response.errCode) {
        message.error(response.msg);
      } else {
        const actionText =
          adminActionType === 'cancel'
            ? '取消'
            : adminActionType === 'refund'
            ? '申请退款'
            : '删除';
        message.success(`${actionText}成功`);
        setAdminActionVisible(false);
        // 刷新详细列表
        handleRefreshCurrentTab();
      }
    } catch (error) {
      console.error('管理员操作失败:', error);
      message.error('操作失败，请重试');
    } finally {
      setAdminActionLoading(false);
    }
  };

  /** 管理员操作 - 取消弹窗 */
  const handleAdminActionCancel = () => {
    setAdminActionVisible(false);
    setCurrent(undefined);
  };

  /** 生成查看信息下拉菜单 */
  const getViewMenuItems = (record: API.Order): MenuProps['items'] => {
    const items: MenuProps['items'] = [];

    // 查看评价 - 扩展到已完成状态
    if (
      [OrderStatus.已完成, OrderStatus.已评价].includes(record.status as any)
    ) {
      items.push({
        key: 'review',
        label: '查看评价',
        onClick: () => handleViewReview(record),
      });
    }

    // 查看投诉记录 - 所有状态都可以查看
    const complaintsCount = getUnhandledComplaintsCount(record.id);
    const hasComplaints = orderComplaints[record.id]?.length > 0;

    items.push({
      key: 'complaints',
      label: (
        <Space>
          查看投诉
          {complaintsCount > 0 && (
            <Tooltip title={`有 ${complaintsCount} 条未处理投诉`}>
              <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
            </Tooltip>
          )}
          {hasComplaints && !complaintsCount && (
            <span style={{ color: '#52c41a' }}>
              ({orderComplaints[record.id].length})
            </span>
          )}
        </Space>
      ),
      onClick: () => handleViewComplaints(record),
    });

    if (
      [OrderStatus.已完成, OrderStatus.已评价].includes(record.status as any)
    ) {
      items.push({
        key: 'photos',
        label: '查看服务照片',
        onClick: () => handleViewServicePhotos(record),
      });
    }

    if (
      record.hasSpecialNote &&
      [OrderStatus.服务中, OrderStatus.已完成, OrderStatus.已评价].includes(
        record.status as any,
      )
    ) {
      items.push({
        key: 'special',
        label: '特殊情况',
        onClick: () => handleViewSpecialNotes(record),
      });
    }

    // 查看服务时长
    if (['已完成', '已评价'].includes(record.status)) {
      items.push({
        key: 'duration',
        label: '查看时长',
        onClick: async () => {
          try {
            const { errCode, data } = await getOrderDurationStatistics(
              record.id,
            );
            if (!errCode && data) {
              Modal.info({
                title: `订单 ${record.sn} - 服务时长统计`,
                width: 1000,
                content: <QuickView orderStatistics={data} />,
              });
            }
          } catch (error) {
            message.error('获取时长统计失败');
          }
        },
      });
    }

    return items;
  };

  /** 生成订单管理下拉菜单 */
  const getManageMenuItems = (record: API.Order): MenuProps['items'] => {
    const items: MenuProps['items'] = [];

    // 修改地址
    items.push({
      key: 'address',
      label: '修改地址',
      onClick: () => {
        setCurrent(record);
        setUpdateAddressVisible(true);
      },
    });

    // 录入投诉
    items.push({
      key: 'complaint',
      label: '录入投诉',
      onClick: () => {
        setCurrent(record);
        setComplaintVisible(true);
      },
    });

    // 派单
    if (record.status === OrderStatus.待接单) {
      items.push({
        key: 'assign',
        label: '派单',
        onClick: () => {
          setCurrent(record);
          setModalVisible(true);
        },
      });
    }

    // 改派订单
    if (
      [OrderStatus.待服务, OrderStatus.已出发].includes(record.status as any)
    ) {
      items.push({
        key: 'reassign',
        label: '改派订单',
        onClick: () => {
          setCurrent(record);
          setModalVisible(true);
        },
      });
    }

    return items;
  };

  /** 生成财务操作下拉菜单 */
  const getFinanceMenuItems = (record: API.Order): MenuProps['items'] => {
    const items: MenuProps['items'] = [];

    // 退款审核
    if ([OrderStatus.退款中].includes(record.status as any)) {
      items.push({
        key: 'audit',
        label: '退款审核',
        onClick: () => handleAuditRefund(record),
      });
    }

    // 退款
    if ([OrderStatus.待接单].includes(record.status as any)) {
      items.push({
        key: 'refund',
        label: <span style={{ color: '#ff4d4f' }}>退款</span>,
        onClick: () => {
          Modal.confirm({
            title: '确定退款吗？',
            icon: <ExclamationCircleOutlined />,
            onOk() {
              handleRefund(record);
            },
          });
        },
      });
    }

    // 取消订单
    if (record.status === OrderStatus.待付款) {
      items.push({
        key: 'cancel',
        label: <span style={{ color: '#ff4d4f' }}>取消订单</span>,
        onClick: () => {
          Modal.confirm({
            title: '确定取消吗？',
            icon: <ExclamationCircleOutlined />,
            onOk() {
              handleCancel(record);
            },
          });
        },
      });
    }

    // 删除
    if (record.status === OrderStatus.已取消) {
      items.push({
        key: 'delete',
        label: <span style={{ color: '#ff4d4f' }}>删除</span>,
        onClick: () => {
          Modal.confirm({
            title: '确定删除吗？',
            icon: <ExclamationCircleOutlined />,
            onOk() {
              handleDel(record);
            },
          });
        },
      });
    }

    return items;
  };

  /** 生成管理员操作下拉菜单 */
  const getAdminMenuItems = (record: API.Order): MenuProps['items'] => {
    const items: MenuProps['items'] = [];

    // 管理员取消
    if (
      ![OrderStatus.已取消, OrderStatus.已退款].includes(record.status as any)
    ) {
      items.push({
        key: 'admin-cancel',
        label: '管理员取消',
        onClick: () => handleAdminAction(record, 'cancel'),
        style: { color: '#ff7a45' },
      });
    }

    // 管理员退款
    if (
      ![OrderStatus.退款中, OrderStatus.已退款].includes(record.status as any)
    ) {
      items.push({
        key: 'admin-refund',
        label: '管理员退款',
        onClick: () => handleAdminAction(record, 'refund'),
        style: { color: '#ff4d4f' },
      });
    }

    // 管理员删除
    items.push({
      key: 'admin-delete',
      label: '管理员删除',
      onClick: () => handleAdminAction(record, 'delete'),
      style: { color: '#cf1322' },
    });

    return items;
  };

  const columns: ProColumns<API.Order, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '订单号',
      dataIndex: 'sn',
      key: 'sn',
      width: 120,
      fixed: 'left',
      copyable: true,
    },
    {
      title: '服务信息',
      dataIndex: 'serviceInfo',
      key: 'serviceInfo',
      width: 150,
      render: (_, record) => {
        const serviceName = record.orderDetails?.[0]?.service?.serviceName;
        const serviceType =
          record.orderDetails?.[0]?.service?.serviceType?.name;
        return (
          <div>
            <div style={{ fontWeight: 500, marginBottom: 2 }}>
              {serviceName}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>{serviceType}</div>
          </div>
        );
      },
      hideInSearch: true,
    },

    {
      title: '客户信息',
      dataIndex: 'customerInfo',
      key: 'customerInfo',
      width: 140,
      render: (_, record) => {
        const nickname = record.customer?.nickname;
        const phone = record.customer?.phone;
        return (
          <div>
            <div style={{ fontWeight: 500, marginBottom: 2 }}>
              {nickname || '-'}
            </div>
            <div
              style={{ fontSize: '12px', color: '#666', cursor: 'pointer' }}
              onClick={() => navigator.clipboard?.writeText(phone || '')}
            >
              {phone || '-'}
            </div>
          </div>
        );
      },
      hideInSearch: true,
    },
    {
      title: '预约时间',
      dataIndex: 'serviceTime',
      key: 'serviceTime',
      width: 100,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '订单金额',
      dataIndex: 'totalFee',
      key: 'totalFee',
      width: 100,
      valueType: 'money',
      hideInSearch: true,
    },
    {
      title: '追加服务',
      dataIndex: 'hasAdditionalServices',
      key: 'hasAdditionalServices',
      width: 80,
      render: (_, record) => {
        if (!record.hasAdditionalServices) {
          return '无';
        }
        return (
          <span style={{ color: '#1890ff' }}>
            有 (¥{record.additionalServiceAmount})
          </span>
        );
      },
      hideInSearch: true,
    },
    {
      title: '特殊情况',
      dataIndex: 'hasSpecialNote',
      key: 'hasSpecialNote',
      width: 80,
      render: (_, record) => {
        return record.hasSpecialNote ? (
          <Tag color="orange" icon={<ExclamationCircleOutlined />}>
            有
          </Tag>
        ) : (
          <span style={{ color: '#999' }}>无</span>
        );
      },
      hideInSearch: true,
    },
    {
      title: '用户备注',
      dataIndex: 'userRemark',
      key: 'userRemark',
      width: 120,
      ellipsis: true,
      render: (_, record) => {
        return record.orderDetails?.[0]?.userRemark || '-';
      },
      hideInSearch: true,
    },
    {
      title: '服务人员',
      dataIndex: ['employee', 'name'],
      key: 'employeename',
      width: 100,
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      valueEnum: {
        待付款: '待付款',
        待接单: '待接单',
        待服务: '待服务',
        已出发: '已出发',
        服务中: '服务中',
        已完成: '已完成',
        已评价: '已评价',
        已取消: '已取消',
        已退款: '已退款',
      },
      filters: true,
      hideInSearch: true,
      render: (_, record) => {
        const complaintsCount = getUnhandledComplaintsCount(record.id);
        return (
          <Space>
            <span>{record.status}</span>
            {complaintsCount > 0 && (
              <Tooltip
                title={`有 ${complaintsCount} 条未处理投诉，点击查看详情`}
              >
                <ExclamationCircleOutlined
                  style={{ color: '#ff4d4f', cursor: 'pointer' }}
                  onClick={() => handleViewComplaints(record)}
                />
              </Tooltip>
            )}
          </Space>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 100,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      width: 220,
      align: 'center',
      render: (_, record) => {
        const viewMenuItems = getViewMenuItems(record);
        const manageMenuItems = getManageMenuItems(record);
        const financeMenuItems = getFinanceMenuItems(record);
        const adminMenuItems = getAdminMenuItems(record);

        return (
          <Space>
            {/* 基础操作 - 始终显示 */}
            <a
              onClick={() => {
                setCurrent(record);
                setShowDetail(true);
              }}
            >
              详情
            </a>
            <a
              onClick={() => {
                setCurrent(record);
                setShowLog(true);
              }}
            >
              日志
            </a>

            {/* 快速操作 - 根据状态显示 */}
            {[OrderStatus.待服务, OrderStatus.已出发].includes(
              record.status as any,
            ) && (
              <Popconfirm
                title="确定要通过后台将服务状态改为【已开始】吗？"
                onConfirm={() => handleStart(record, record.employeeId!)}
                okText="确定"
                cancelText="取消"
              >
                <a>开始服务</a>
              </Popconfirm>
            )}
            {record.status === OrderStatus.服务中 && (
              <Popconfirm
                title="确定要通过后台将服务状态改为【已完成】吗？"
                onConfirm={() => handleComplete(record, record.employeeId!)}
                okText="确定"
                cancelText="取消"
              >
                <a>完成订单</a>
              </Popconfirm>
            )}

            {/* 更多操作下拉菜单 - 分组显示 */}
            <Dropdown
              menu={{
                items: [
                  // 查看信息组
                  ...(viewMenuItems && viewMenuItems.length > 0
                    ? [
                        {
                          type: 'group',
                          label: '查看信息',
                          children: viewMenuItems,
                        },
                      ]
                    : []),

                  // 订单管理组
                  ...(manageMenuItems && manageMenuItems.length > 0
                    ? [
                        ...(viewMenuItems && viewMenuItems.length > 0
                          ? [{ type: 'divider' }]
                          : []),
                        {
                          type: 'group',
                          label: '订单管理',
                          children: manageMenuItems,
                        },
                      ]
                    : []),

                  // 财务操作组
                  ...(financeMenuItems && financeMenuItems.length > 0
                    ? [
                        ...((viewMenuItems && viewMenuItems.length > 0) ||
                        (manageMenuItems && manageMenuItems.length > 0)
                          ? [{ type: 'divider' }]
                          : []),
                        {
                          type: 'group',
                          label: '财务操作',
                          children: financeMenuItems,
                        },
                      ]
                    : []),

                  // 管理员操作组
                  ...(adminMenuItems && adminMenuItems.length > 0
                    ? [
                        ...((viewMenuItems && viewMenuItems.length > 0) ||
                        (manageMenuItems && manageMenuItems.length > 0) ||
                        (financeMenuItems && financeMenuItems.length > 0)
                          ? [{ type: 'divider' }]
                          : []),
                        {
                          type: 'group',
                          label: '管理员操作',
                          children: adminMenuItems,
                        },
                      ]
                    : []),
                ].filter(Boolean) as MenuProps['items'],
              }}
              trigger={['click']}
            >
              <a onClick={(e) => e.preventDefault()}>
                更多 <DownOutlined />
              </a>
            </Dropdown>
          </Space>
        );
      },
    },
  ];

  const tabItems = [
    // {
    //   key: 'overview',
    //   label: '概览仪表板',
    //   children: <OverviewDashboard />,
    // },
    {
      key: 'board',
      label: '状态看板',
      children: (
        <StatusBoardView
          ref={statusBoardRef}
          onViewDetail={(order) => {
            setCurrent(order);
            setShowDetail(true);
          }}
          onViewLog={(order) => {
            setCurrent(order);
            setShowLog(true);
          }}
          onAssign={(order) => {
            setCurrent(order);
            setModalVisible(true);
          }}
          onStart={handleStart}
          onComplete={handleComplete}
          onViewReview={handleViewReview}
          onAuditRefund={handleAuditRefund}
          onViewServicePhotos={handleViewServicePhotos}
          onViewSpecialNotes={handleViewSpecialNotes}
          onUpdateAddress={(order) => {
            setCurrent(order);
            setUpdateAddressVisible(true);
          }}
        />
      ),
    },
    {
      key: 'timeline',
      label: '时间安排',
      children: (
        <TimelineView
          ref={timelineRef}
          onViewDetail={(order) => {
            setCurrent(order);
            setShowDetail(true);
          }}
          onAssign={(order) => {
            setCurrent(order);
            setModalVisible(true);
          }}
          onUpdateAddress={(order) => {
            setCurrent(order);
            setUpdateAddressVisible(true);
          }}
        />
      ),
    },
    {
      key: 'table',
      label: '详细列表',
      children: (
        <DetailList
          ref={detailListRef}
          dictionarie={dictionarie}
          columns={columns}
          checkOrderComplaints={checkOrderComplaints}
          getUnhandledComplaintsCount={getUnhandledComplaintsCount}
          handleViewComplaints={handleViewComplaints}
          setCurrent={setCurrent}
          setModalVisible={setModalVisible}
        />
      ),
    },
  ];

  return (
    <>
      <Card>
        <Tabs
          defaultActiveKey="board"
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          tabBarExtraContent={
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefreshCurrentTab}
              size="small"
            >
              刷新
            </Button>
          }
        />
      </Card>
      <AcceptModal
        open={modalVisible}
        current={current}
        onClose={() => {
          setModalVisible(false);
        }}
        onSave={async (employeeId: number) => {
          if (current) {
            await handleAccept(current, employeeId);
          }
        }}
      />
      <DetailModal
        open={showDetail}
        current={current}
        onClose={() => {
          setShowDetail(false);
        }}
        onRefresh={() => {
          // 刷新列表
          handleRefreshCurrentTab();
        }}
      />
      <OrderLog
        open={showLog}
        order={current}
        onClose={() => {
          setShowLog(false);
        }}
      />
      <RefundAuditModal
        open={refundAuditVisible}
        current={current}
        onClose={() => {
          setRefundAuditVisible(false);
        }}
        onSuccess={() => {
          // 刷新列表
          handleRefreshCurrentTab();
        }}
      />
      <ReviewDetailModal
        visible={reviewDetailVisible}
        order={current || null}
        onClose={() => {
          setReviewDetailVisible(false);
        }}
      />
      <ServicePhotosModal
        visible={servicePhotosVisible}
        orderId={current?.id}
        onClose={() => {
          setServicePhotosVisible(false);
        }}
      />
      <SpecialNotesModal
        open={specialNotesVisible}
        order={current}
        onClose={() => {
          setSpecialNotesVisible(false);
        }}
      />
      <UpdateAddressModal
        visible={updateAddressVisible}
        order={current}
        onClose={() => setUpdateAddressVisible(false)}
        onSuccess={() => {
          setUpdateAddressVisible(false);
          // 刷新列表
          handleRefreshCurrentTab();
        }}
      />
      <AdminActionModal
        open={adminActionVisible}
        onCancel={handleAdminActionCancel}
        onConfirm={handleAdminActionConfirm}
        title={`管理员${
          adminActionType === 'cancel'
            ? '取消'
            : adminActionType === 'refund'
            ? '申请退款'
            : '删除'
        }订单`}
        content={`确定要${
          adminActionType === 'cancel'
            ? '取消'
            : adminActionType === 'refund'
            ? '申请退款'
            : '删除'
        }订单 ${current?.sn} 吗？`}
        actionType={adminActionType}
        loading={adminActionLoading}
      />

      {/* 投诉录入模态框 */}
      <OrderComplaintModal
        visible={complaintVisible}
        order={current}
        onClose={() => {
          setComplaintVisible(false);
          setCurrent(undefined);
        }}
        onSuccess={() => {
          setComplaintVisible(false);
          setCurrent(undefined);
          // 刷新列表
          handleRefreshCurrentTab();
        }}
      />

      {/* 投诉列表查看模态框 */}
      <OrderComplaintListModal
        visible={complaintListVisible}
        order={current}
        onClose={() => {
          setComplaintListVisible(false);
          setCurrent(undefined);
        }}
      />
    </>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(Appointment);
