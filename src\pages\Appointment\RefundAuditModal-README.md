# 退款审核界面优化 (v2.0)

## 重大更新 - 基于新的统一退款审核接口

### 1. 接口升级
- **新增**: `getOrderRefundInfo` - 获取订单详细退款信息
- **新增**: `unifiedRefundAudit` - 统一退款审核接口
- **保留**: 原有的申请流程不变，仅优化审核界面

### 2. 数据获取优化
- 审核界面打开时自动调用 `getOrderRefundInfo` 接口获取详细信息
- 显示完整的订单信息：客户信息、员工信息、订单状态等
- 根据接口返回的 `canRefund` 字段判断是否可退款
- 显示每个订单项的详细信息：原价、优惠、可退金额等

### 3. 全新的退款设置界面
- **主订单退款**: 独立的退款金额设置和卡券退还选项
- **追加服务退款**: 支持多个追加服务的独立退款设置
- **卡券退还控制**: 每个订单项可单独设置是否退还使用的卡券
- **详细信息展示**: 显示服务内容、订单状态、优惠明细等

### 4. 智能操作功能
- **快捷设置**: 支持全额、80%、60%、40%、20% 的快捷退款比例
- **按比例分配**: 快捷按钮会自动按比例设置所有可退款项目
- **实时验证**: 退款金额不能超过可退金额，实时校验
- **灵活配置**: 支持部分退款、选择性退还卡券

### 5. 统一审核处理
- 使用新的 `unifiedRefundAudit` 接口进行审核
- 支持批量处理主订单和多个追加服务的退款
- 详细的审核结果反馈，显示每个项目的处理状态
- 成功后关闭弹窗并刷新列表，失败时仅显示错误提示

## 使用说明

### 界面展示
1. **订单信息区域**：
   - 服务名称
   - 主订单金额
   - 追加订单金额（如有）
   - 订单总金额
   - 申请时间

2. **退款设置区域**：
   - 审核结果选择（同意/不同意）
   - 总退款金额输入
   - 主订单退款金额输入
   - 追加订单退款金额输入（如有追加服务）
   - 快捷百分比按钮（全额、80%、60%、40%、20%）

### 操作流程
1. 选择审核结果（同意/不同意）
2. 如果同意退款：
   - 可以使用百分比按钮快速设置退款比例
   - 或者手动输入具体的退款金额
   - 系统会自动验证金额的合理性
3. 如果不同意退款：
   - 输入驳回原因
4. 点击确定提交审核结果

### 技术特性
- 响应式表单验证
- 实时金额计算
- 数据一致性检查
- 用户友好的交互体验

## 注意事项
- 退款金额不能超过对应订单的原始金额
- 总退款金额必须等于主订单和追加订单退款金额之和
- 只有当订单包含追加服务时才会显示追加订单相关字段

## 代码优化
### toFixed方法替换
为了避免编译错误，已将所有直接使用的 `toFixed()` 方法替换为全局封装的格式化方法：

#### 修改的文件：
1. **src/pages/Appointment/RefundAuditModal.tsx**
   - 使用 `formatAmount()` 替换金额格式化

2. **src/pages/EmployeeActionStats/components/ActionOverview.tsx**
   - 使用 `formatNumber()` 替换百分比格式化

3. **src/pages/Coupon/RightsCard/Distribution/CardView/index.tsx**
   - 使用 `formatNumber()` 替换折扣率格式化

4. **src/pages/Coupon/RightsCard/Distribution/UserView/CardList.tsx**
   - 使用 `formatNumber()` 替换折扣率格式化

5. **src/pages/Coupon/RightsCard/Type/index.tsx**
   - 使用 `formatNumber()` 替换折扣率格式化

6. **src/pages/Analysis/Order/components/TimePeriodStats.tsx**
   - 使用 `formatAmount()` 替换金额格式化

7. **src/services/service-duration-statistics.ts**
   - 使用 `formatNumber()` 替换百分比格式化

#### 使用的全局格式化方法：
- `formatAmount(amount, decimals)`: 安全地格式化金额
- `formatNumber(value, decimals)`: 安全地格式化数字
- `formatCurrency(amount, currency, decimals)`: 格式化金额并添加货币符号

这些方法都包含了数据验证，避免了运行时错误。

## 新版本技术实现 (v2.0)

### 组件架构
- **数据加载**: 使用 `Spin` 组件显示加载状态，提升用户体验
- **卡片布局**: 使用 `Card` 组件分组显示订单信息和退款项目
- **动态渲染**: 根据接口返回的数据动态生成退款项目表单
- **状态管理**: 分离数据加载状态和提交状态，精确控制UI状态

### 接口集成
- **getOrderRefundInfo**: 获取详细的订单退款信息
- **unifiedRefundAudit**: 统一处理多项目退款审核
- **错误处理**: 完善的错误提示和异常处理机制
- **响应处理**: 根据接口返回结果提供详细的操作反馈

### 表单设计
- **动态字段**: 根据订单数据动态生成表单字段
- **实时验证**: 输入时即时验证金额范围和必填项
- **批量操作**: 支持快捷设置所有退款项目的比例
- **独立控制**: 每个退款项目可独立设置金额和卡券退还

### 用户体验优化
- **信息展示**: 详细显示订单状态、优惠明细、可退金额等
- **操作引导**: 清晰的标签和说明文字，降低操作难度
- **快捷操作**: 提供常用比例的快捷按钮
- **反馈机制**: 及时的成功/失败提示和详细的错误信息

### 数据安全
- **金额验证**: 严格验证退款金额不超过可退金额
- **必填校验**: 确保必要信息的完整性
- **类型安全**: 使用 TypeScript 确保数据类型正确
- **异常处理**: 完善的错误捕获和用户友好的错误提示
