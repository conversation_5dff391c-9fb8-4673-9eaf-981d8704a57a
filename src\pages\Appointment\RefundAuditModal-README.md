# 退款审核界面优化

## 修改内容

### 1. 新增追加订单金额显示
- 在退款审核弹窗中新增了追加订单金额的显示
- 使用 `Descriptions` 组件清晰展示主订单金额、追加订单金额和订单总金额
- 只有当订单有追加服务时才显示追加订单金额

### 2. 支持分别设置退款金额
- **总退款金额**: 显示整体退款金额
- **主订单退款金额**: 可单独设置主订单的退款金额
- **追加订单退款金额**: 可单独设置追加订单的退款金额（仅当有追加服务时显示）

### 3. 智能金额计算
- 当修改主订单或追加订单退款金额时，总退款金额会自动更新
- 百分比按钮会按比例分配退款金额到主订单和追加订单
- 提交时会验证总退款金额与分项金额的一致性

### 4. API 接口扩展
- 扩展了 `auditRefund` API 接口，支持传递主订单和追加订单的退款金额
- 新增参数：
  - `mainOrderMoney`: 主订单退款金额
  - `additionalOrderMoney`: 追加订单退款金额

## 使用说明

### 界面展示
1. **订单信息区域**：
   - 服务名称
   - 主订单金额
   - 追加订单金额（如有）
   - 订单总金额
   - 申请时间

2. **退款设置区域**：
   - 审核结果选择（同意/不同意）
   - 总退款金额输入
   - 主订单退款金额输入
   - 追加订单退款金额输入（如有追加服务）
   - 快捷百分比按钮（全额、80%、60%、40%、20%）

### 操作流程
1. 选择审核结果（同意/不同意）
2. 如果同意退款：
   - 可以使用百分比按钮快速设置退款比例
   - 或者手动输入具体的退款金额
   - 系统会自动验证金额的合理性
3. 如果不同意退款：
   - 输入驳回原因
4. 点击确定提交审核结果

### 技术特性
- 响应式表单验证
- 实时金额计算
- 数据一致性检查
- 用户友好的交互体验

## 注意事项
- 退款金额不能超过对应订单的原始金额
- 总退款金额必须等于主订单和追加订单退款金额之和
- 只有当订单包含追加服务时才会显示追加订单相关字段

## 代码优化
### toFixed方法替换
为了避免编译错误，已将所有直接使用的 `toFixed()` 方法替换为全局封装的格式化方法：

#### 修改的文件：
1. **src/pages/Appointment/RefundAuditModal.tsx**
   - 使用 `formatAmount()` 替换金额格式化

2. **src/pages/EmployeeActionStats/components/ActionOverview.tsx**
   - 使用 `formatNumber()` 替换百分比格式化

3. **src/pages/Coupon/RightsCard/Distribution/CardView/index.tsx**
   - 使用 `formatNumber()` 替换折扣率格式化

4. **src/pages/Coupon/RightsCard/Distribution/UserView/CardList.tsx**
   - 使用 `formatNumber()` 替换折扣率格式化

5. **src/pages/Coupon/RightsCard/Type/index.tsx**
   - 使用 `formatNumber()` 替换折扣率格式化

6. **src/pages/Analysis/Order/components/TimePeriodStats.tsx**
   - 使用 `formatAmount()` 替换金额格式化

7. **src/services/service-duration-statistics.ts**
   - 使用 `formatNumber()` 替换百分比格式化

#### 使用的全局格式化方法：
- `formatAmount(amount, decimals)`: 安全地格式化金额
- `formatNumber(value, decimals)`: 安全地格式化数字
- `formatCurrency(amount, currency, decimals)`: 格式化金额并添加货币符号

这些方法都包含了数据验证，避免了运行时错误。
