import { getOrderRefundInfo, unifiedRefundAudit } from '@/services/order';
import { formatAmount } from '@/utils/format';
import {
  Button,
  Card,
  Checkbox,
  Descriptions,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Radio,
  Space,
  Spin,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';

type RefundAuditModalProps = {
  open: boolean;
  current?: API.Order;
  onClose: () => void;
  onSuccess: () => void;
};

const RefundAuditModal: React.FC<RefundAuditModalProps> = ({
  open,
  current,
  onClose,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [result, setResult] = useState<boolean>(true);
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(false);
  const [refundInfo, setRefundInfo] = useState<API.OrderRefundInfo | null>(null);

  // 获取订单退款信息
  const fetchRefundInfo = async (orderId: number) => {
    try {
      setDataLoading(true);
      const response = await getOrderRefundInfo(orderId);
      if (response.errCode === 0) {
        setRefundInfo(response.data);
        // 初始化表单数据
        initializeForm(response.data);
      } else {
        message.error(response.msg || '获取退款信息失败');
      }
    } catch (error) {
      console.error('获取退款信息失败:', error);
      message.error('获取退款信息失败');
    } finally {
      setDataLoading(false);
    }
  };

  // 初始化表单数据
  const initializeForm = (data: API.OrderRefundInfo) => {
    const formData: any = {
      result: true,
    };

    // 主订单退款信息
    if (data.mainOrder.canRefund) {
      formData.mainOrderRefundAmount = data.mainOrder.refundableAmount;
      formData.mainOrderRefundCoupons = true;
    }

    // 追加服务退款信息
    data.additionalServices.forEach((service, index) => {
      if (service.canRefund) {
        formData[`additionalService_${index}_refundAmount`] = service.refundableAmount;
        formData[`additionalService_${index}_refundCoupons`] = true;
      }
    });

    form.setFieldsValue(formData);
    setResult(true);
  };

  useEffect(() => {
    if (open && current?.id) {
      fetchRefundInfo(current.id);
    } else {
      form.resetFields();
      setRefundInfo(null);
    }
  }, [open, current, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (!refundInfo) {
        message.error('退款信息未加载');
        return;
      }

      // 构建退款项目列表
      const refundItems: API.UnifiedRefundAuditRequest['refundItems'] = [];

      // 主订单退款项目
      if (refundInfo.mainOrder.canRefund && values.mainOrderRefundAmount > 0) {
        refundItems.push({
          type: 'main_order',
          id: refundInfo.mainOrder.orderSn,
          refundAmount: values.mainOrderRefundAmount,
          shouldRefundCoupons: values.mainOrderRefundCoupons || false,
        });
      }

      // 追加服务退款项目
      refundInfo.additionalServices.forEach((service, index) => {
        const refundAmount = values[`additionalService_${index}_refundAmount`];
        const shouldRefundCoupons = values[`additionalService_${index}_refundCoupons`];

        if (service.canRefund && refundAmount > 0) {
          refundItems.push({
            type: 'additional_service',
            id: service.id,
            refundAmount,
            shouldRefundCoupons: shouldRefundCoupons || false,
          });
        }
      });

      if (values.result && refundItems.length === 0) {
        message.error('请至少选择一个退款项目');
        return;
      }

      // 调用统一退款审核接口
      const auditData: API.UnifiedRefundAuditRequest = {
        operatorId: 1, // TODO: 从用户信息中获取操作员ID
        result: values.result,
        reason: values.reason,
        refundItems,
      };

      const response = await unifiedRefundAudit(auditData);

      if (response.errCode === 0) {
        const result = response.data;
        if (result.success) {
          message.success(result.message);
        } else {
          message.warning(result.message);
        }
        onSuccess();
        onClose();
      } else {
        message.error(response.msg || '审核失败');
      }
    } catch (error) {
      console.error('审核失败:', error);
      message.error('审核失败');
    } finally {
      setLoading(false);
    }
  };

  // 快捷设置退款比例
  const handlePercentageClick = (percentage: number) => {
    if (!refundInfo) return;

    const formData: any = {};

    // 主订单按比例退款
    if (refundInfo.mainOrder.canRefund) {
      const refundAmount = (refundInfo.mainOrder.refundableAmount * percentage) / 100;
      formData.mainOrderRefundAmount = refundAmount;
    }

    // 追加服务按比例退款
    refundInfo.additionalServices.forEach((service, index) => {
      if (service.canRefund) {
        const refundAmount = (service.refundableAmount * percentage) / 100;
        formData[`additionalService_${index}_refundAmount`] = refundAmount;
      }
    });

    form.setFieldsValue(formData);
  };

  return (
    <Modal
      title="退款审核"
      open={open}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
          disabled={dataLoading}
        >
          确定
        </Button>,
      ]}
    >
      {dataLoading ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>正在加载退款信息...</div>
        </div>
      ) : refundInfo ? (
        <>
          {/* 订单基本信息 */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Typography.Title level={5} style={{ margin: 0 }}>
              订单信息
            </Typography.Title>
            <Descriptions size="small" column={2} style={{ marginTop: 12 }}>
              <Descriptions.Item label="订单编号">
                {refundInfo.orderSn}
              </Descriptions.Item>
              <Descriptions.Item label="客户信息">
                {refundInfo.customer.nickname} ({refundInfo.customer.phone})
              </Descriptions.Item>
              <Descriptions.Item label="服务员工">
                {refundInfo.employee.name} ({refundInfo.employee.phone})
              </Descriptions.Item>
              <Descriptions.Item label="可退款总额">
                ¥{formatAmount(refundInfo.summary.totalRefundableAmount, 2)}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 审核表单 */}
          <Form form={form} layout="vertical">
            <Form.Item
              name="result"
              label="审核结果"
              rules={[{ required: true, message: '请选择审核结果' }]}
            >
              <Radio.Group onChange={(e) => setResult(e.target.value)}>
                <Radio value={true}>同意退款</Radio>
                <Radio value={false}>拒绝退款</Radio>
              </Radio.Group>
            </Form.Item>

            {result ? (
              <>
                {/* 快捷操作按钮 */}
                <Form.Item label="快捷设置">
                  <Space>
                    <Button size="small" onClick={() => handlePercentageClick(100)}>
                      全额退款
                    </Button>
                    <Button size="small" onClick={() => handlePercentageClick(80)}>
                      80%
                    </Button>
                    <Button size="small" onClick={() => handlePercentageClick(60)}>
                      60%
                    </Button>
                    <Button size="small" onClick={() => handlePercentageClick(40)}>
                      40%
                    </Button>
                    <Button size="small" onClick={() => handlePercentageClick(20)}>
                      20%
                    </Button>
                  </Space>
                </Form.Item>

                {/* 主订单退款设置 */}
                {refundInfo.mainOrder.canRefund && (
                  <Card size="small" style={{ marginBottom: 16 }}>
                    <Typography.Text strong>主订单退款</Typography.Text>
                    <Descriptions size="small" column={2} style={{ marginTop: 8, marginBottom: 12 }}>
                      <Descriptions.Item label="订单状态">
                        {refundInfo.mainOrder.status}
                      </Descriptions.Item>
                      <Descriptions.Item label="可退金额">
                        ¥{formatAmount(refundInfo.mainOrder.refundableAmount, 2)}
                      </Descriptions.Item>
                    </Descriptions>

                    <Form.Item
                      name="mainOrderRefundAmount"
                      label="退款金额"
                      rules={[
                        { required: true, message: '请输入退款金额' },
                        {
                          type: 'number',
                          min: 0,
                          max: refundInfo.mainOrder.refundableAmount,
                          message: `退款金额不能超过¥${formatAmount(refundInfo.mainOrder.refundableAmount, 2)}`
                        }
                      ]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        min={0}
                        max={refundInfo.mainOrder.refundableAmount}
                        precision={2}
                        addonBefore="¥"
                      />
                    </Form.Item>

                    <Form.Item
                      name="mainOrderRefundCoupons"
                      valuePropName="checked"
                    >
                      <Checkbox>退还使用的卡券</Checkbox>
                    </Form.Item>
                  </Card>
                )}

                {/* 追加服务退款设置 */}
                {refundInfo.additionalServices.map((service, index) => (
                  service.canRefund && (
                    <Card key={service.id} size="small" style={{ marginBottom: 16 }}>
                      <Typography.Text strong>追加服务退款 #{index + 1}</Typography.Text>
                      <Descriptions size="small" column={2} style={{ marginTop: 8, marginBottom: 12 }}>
                        <Descriptions.Item label="服务内容">
                          {service.serviceNames}
                        </Descriptions.Item>
                        <Descriptions.Item label="订单状态">
                          {service.status}
                        </Descriptions.Item>
                        <Descriptions.Item label="原价">
                          ¥{formatAmount(service.originalPrice, 2)}
                        </Descriptions.Item>
                        <Descriptions.Item label="可退金额">
                          ¥{formatAmount(service.refundableAmount, 2)}
                        </Descriptions.Item>
                        {service.hasDiscounts && (
                          <>
                            <Descriptions.Item label="卡扣减">
                              ¥{formatAmount(service.cardDeduction, 2)}
                            </Descriptions.Item>
                            <Descriptions.Item label="券扣减">
                              ¥{formatAmount(service.couponDeduction, 2)}
                            </Descriptions.Item>
                          </>
                        )}
                      </Descriptions>

                      <Form.Item
                        name={`additionalService_${index}_refundAmount`}
                        label="退款金额"
                        rules={[
                          { required: true, message: '请输入退款金额' },
                          {
                            type: 'number',
                            min: 0,
                            max: service.refundableAmount,
                            message: `退款金额不能超过¥${formatAmount(service.refundableAmount, 2)}`
                          }
                        ]}
                      >
                        <InputNumber
                          style={{ width: '100%' }}
                          min={0}
                          max={service.refundableAmount}
                          precision={2}
                          addonBefore="¥"
                        />
                      </Form.Item>

                      <Form.Item
                        name={`additionalService_${index}_refundCoupons`}
                        valuePropName="checked"
                      >
                        <Checkbox>退还使用的卡券</Checkbox>
                      </Form.Item>
                    </Card>
                  )
                ))}
              </>
            ) : (
              <Form.Item
                name="reason"
                label="拒绝原因"
                rules={[{ required: true, message: '请输入拒绝原因' }]}
              >
                <Input.TextArea rows={4} maxLength={200} showCount placeholder="请详细说明拒绝退款的原因" />
              </Form.Item>
            )}
          </Form>
        </>
      ) : (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Typography.Text type="secondary">暂无退款信息</Typography.Text>
        </div>
      )}
    </Modal>
  );
};

export default RefundAuditModal;
