import { auditRefund } from '@/services/order';
import { formatAmount } from '@/utils/format';
import {
  Button,
  Descriptions,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Radio,
  Space,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';

type RefundAuditModalProps = {
  open: boolean;
  current?: API.Order;
  onClose: () => void;
  onSuccess: () => void;
};

const RefundAuditModal: React.FC<RefundAuditModalProps> = ({
  open,
  current,
  onClose,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [result, setResult] = useState<boolean>(true);
  const [loading, setLoading] = useState(false);

  // 计算总金额（主订单 + 追加订单）
  const getTotalAmount = () => {
    const mainOrderAmount = current?.totalFee || 0;
    const additionalAmount = current?.additionalServiceAmount || 0;
    return mainOrderAmount + additionalAmount;
  };

  // 监听表单值变化，自动计算总退款金额
  const handleFormValuesChange = (changedValues: any, allValues: any) => {
    if (changedValues.mainOrderMoney !== undefined || changedValues.additionalOrderMoney !== undefined) {
      const mainOrderMoney = allValues.mainOrderMoney || 0;
      const additionalOrderMoney = allValues.additionalOrderMoney || 0;
      const totalMoney = mainOrderMoney + additionalOrderMoney;

      form.setFieldsValue({ money: totalMoney });
    }
  };

  useEffect(() => {
    if (open && current) {
      const totalAmount = getTotalAmount();
      form.setFieldsValue({
        result: true,
        money: totalAmount,
        mainOrderMoney: current.totalFee,
        additionalOrderMoney: current.additionalServiceAmount || 0,
      });
      setResult(true);
    } else {
      form.resetFields();
    }
  }, [open, current, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 验证退款金额的合理性
      const mainOrderRefund = values.mainOrderMoney || 0;
      const additionalRefund = values.additionalOrderMoney || 0;
      const totalRefund = mainOrderRefund + additionalRefund;

      if (Math.abs(totalRefund - values.money) > 0.01) {
        message.error('总退款金额与主订单和追加订单退款金额之和不一致');
        return;
      }

      const response = await auditRefund(current?.sn || '', {
        result: values.result,
        reason: values.reason,
        money: values.money,
        mainOrderMoney: values.mainOrderMoney,
        additionalOrderMoney: values.additionalOrderMoney,
      });

      if (response.errCode) {
        message.error(response.msg);
      } else {
        message.success('审核成功');
        onSuccess();
        onClose();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePercentageClick = (percentage: number) => {
    const totalAmount = getTotalAmount();
    const calculatedAmount = (totalAmount * percentage) / 100;

    // 按比例分配到主订单和追加订单
    const mainOrderAmount = current?.totalFee || 0;
    const additionalAmount = current?.additionalServiceAmount || 0;

    if (totalAmount > 0) {
      const mainOrderRefund = (mainOrderAmount / totalAmount) * calculatedAmount;
      const additionalRefund = (additionalAmount / totalAmount) * calculatedAmount;

      form.setFieldsValue({
        money: calculatedAmount,
        mainOrderMoney: mainOrderRefund,
        additionalOrderMoney: additionalRefund,
      });
    }
  };

  return (
    <Modal
      title="退款审核"
      open={open}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          确定
        </Button>,
      ]}
    >
      <Typography.Title level={5}>
        {current?.orderDetails?.[0]?.service?.serviceName || '未知服务'}
      </Typography.Title>

      <Descriptions size="small" column={1} style={{ marginBottom: 16 }}>
        <Descriptions.Item label="主订单金额">
          ¥{current?.totalFee || '0.00'}
        </Descriptions.Item>
        {current?.hasAdditionalServices && (
          <Descriptions.Item label="追加订单金额">
            ¥{current?.additionalServiceAmount || '0.00'}
          </Descriptions.Item>
        )}
        <Descriptions.Item label="订单总金额">
          ¥{formatAmount(getTotalAmount(), 2)}
        </Descriptions.Item>
        <Descriptions.Item label="申请时间">
          {current?.updatedAt
            ? new Date(current.updatedAt).toLocaleString()
            : '未知'}
        </Descriptions.Item>
      </Descriptions>

      <Form
        form={form}
        layout="vertical"
        style={{ marginTop: 20 }}
        onValuesChange={handleFormValuesChange}
      >
        <Form.Item
          name="result"
          label="审核结果"
          rules={[{ required: true, message: '请选择审核结果' }]}
        >
          <Radio.Group onChange={(e) => setResult(e.target.value)}>
            <Radio value={true}>同意</Radio>
            <Radio value={false}>不同意</Radio>
          </Radio.Group>
        </Form.Item>

        {result ? (
          <>
            <Form.Item
              name="money"
              label="总退款金额"
              rules={[{ required: true, message: '请输入总退款金额' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                max={getTotalAmount()}
                precision={2}
                addonBefore="¥"
              />
            </Form.Item>

            <Form.Item
              name="mainOrderMoney"
              label="主订单退款金额"
              rules={[{ required: true, message: '请输入主订单退款金额' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                max={current?.totalFee}
                precision={2}
                addonBefore="¥"
              />
            </Form.Item>

            {current?.hasAdditionalServices && (
              <Form.Item
                name="additionalOrderMoney"
                label="追加订单退款金额"
                rules={[{ required: true, message: '请输入追加订单退款金额' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={current?.additionalServiceAmount}
                  precision={2}
                  addonBefore="¥"
                />
              </Form.Item>
            )}

            <Form.Item>
              <Space>
                <Button size="small" onClick={() => handlePercentageClick(100)}>
                  全额
                </Button>
                <Button size="small" onClick={() => handlePercentageClick(80)}>
                  80%
                </Button>
                <Button size="small" onClick={() => handlePercentageClick(60)}>
                  60%
                </Button>
                <Button size="small" onClick={() => handlePercentageClick(40)}>
                  40%
                </Button>
                <Button size="small" onClick={() => handlePercentageClick(20)}>
                  20%
                </Button>
              </Space>
            </Form.Item>
          </>
        ) : (
          <Form.Item
            name="reason"
            label="驳回原因"
            rules={[{ required: true, message: '请输入驳回原因' }]}
          >
            <Input.TextArea rows={4} maxLength={200} showCount />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default RefundAuditModal;
