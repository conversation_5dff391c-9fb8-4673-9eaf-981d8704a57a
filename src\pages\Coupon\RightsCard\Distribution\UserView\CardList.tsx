import { getCardsByUser, remove } from '@/services/customer-membership-cards';
import { formatNumber } from '@/utils/format';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, message, Popconfirm, Space } from 'antd';
import moment from 'moment';
import React, { useRef, useState } from 'react';
import IssueCardModal from '../components/IssueCardModal';

interface CardListProps {
  userId: number;
}

/**
 * 用户权益卡列表组件
 */
const CardList: React.FC<CardListProps> = ({ userId }) => {
  const actionRef = useRef<ActionType>();

  // 发放权益卡模态框状态
  const [issueModalVisible, setIssueModalVisible] = useState<boolean>(false);
  const [selectedCardTypeId, setSelectedCardTypeId] = useState<number>();

  // 处理撤销发放
  const handleRevoke = async (id: number) => {
    try {
      const response = await remove(id);
      if (response.errCode) {
        message.error(response.msg || '撤销发放失败');
      } else {
        message.success('撤销发放成功');
        actionRef.current?.reload();
      }
    } catch (error) {
      console.error('撤销发放失败', error);
      message.error('撤销发放失败，请重试');
    }
  };

  // 表格列定义
  const columns: ProColumns<API.CustomerMembershipCard>[] = [
    {
      title: '卡ID',
      dataIndex: 'id',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '权益卡名称',
      dataIndex: ['cardType', 'name'],
      ellipsis: true,
      width: 150,
    },
    {
      title: '购买时间',
      dataIndex: 'purchaseTime',
      hideInSearch: true,
      width: 180,
      render: (_, record) =>
        record.purchaseTime
          ? moment(record.purchaseTime).format('YYYY-MM-DD HH:mm:ss')
          : '-',
    },
    {
      title: '到期时间',
      dataIndex: 'expiryTime',
      hideInSearch: true,
      width: 180,
      render: (_, record) =>
        record.expiryTime
          ? moment(record.expiryTime).format('YYYY-MM-DD HH:mm:ss')
          : '-',
    },
    {
      title: '剩余次数',
      dataIndex: 'remainingUses',
      hideInSearch: true,
      width: 100,
      render: (_, record) =>
        record.remainTimes !== undefined
          ? record.remainTimes === -1
            ? '不限'
            : record.remainTimes
          : '-',
    },
    {
      title: '折扣率',
      dataIndex: ['cardType', 'discountRate'],
      hideInSearch: true,
      width: 100,
      render: (_, record) =>
        record.cardType?.discountRate
          ? `${formatNumber(record.cardType.discountRate * 10, 1)}折`
          : '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueEnum: {
        active: { text: '有效', status: 'Success' },
        expired: { text: '已过期', status: 'Error' },
        used: { text: '已用完', status: 'Warning' },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Popconfirm
            title="确定要撤销此权益卡吗？"
            description="撤销后将无法恢复，用户将无法使用此权益卡"
            onConfirm={() => handleRevoke(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger>
              撤销发放
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.CustomerMembershipCard>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        search={false}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
        }}
        options={false}
        request={async (params) => {
          if (!userId) {
            return {
              data: [],
              success: true,
              total: 0,
            };
          }

          const response = await getCardsByUser(userId, params);

          if (response.errCode) {
            message.error(response.msg || '获取权益卡列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }

          return {
            data: response.data?.list || [],
            success: true,
            total: response.data?.total || 0,
          };
        }}
        toolBarRender={() => [
          <Button
            key="issue"
            type="primary"
            onClick={() => {
              setSelectedCardTypeId(undefined);
              setIssueModalVisible(true);
            }}
          >
            发放新权益卡
          </Button>,
        ]}
      />

      {/* 发放权益卡模态框 */}
      <IssueCardModal
        open={issueModalVisible}
        onClose={() => setIssueModalVisible(false)}
        onSuccess={() => {
          setIssueModalVisible(false);
          actionRef.current?.reload();
        }}
        customerId={userId}
        cardTypeId={selectedCardTypeId}
      />
    </>
  );
};

export default CardList;
