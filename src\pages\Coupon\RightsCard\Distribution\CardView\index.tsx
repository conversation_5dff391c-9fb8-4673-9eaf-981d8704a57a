import { index } from '@/services/membership-card-types';
import { formatNumber } from '@/utils/format';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Drawer, message, Space, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import UserList from './UserList';

/**
 * 权益卡视角组件
 */
const CardView: React.FC = () => {
  // 表格操作引用
  const actionRef = useRef<ActionType>();

  // 抽屉状态
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
  const [currentCardId, setCurrentCardId] = useState<number>(0);
  const [currentCardName, setCurrentCardName] = useState<string>('');

  // 处理查看用户列表
  const handleViewUsers = (record: API.MembershipCardType) => {
    setCurrentCardId(record.id);
    setCurrentCardName(record.name);
    setDrawerVisible(true);
  };

  // 表格列定义
  const columns: ProColumns<API.MembershipCardType>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '权益卡名称',
      dataIndex: 'name',
      ellipsis: true,
      width: 150,
    },
    {
      title: '售价',
      dataIndex: 'price',
      hideInSearch: true,
      width: 100,
      render: (_, record) => `¥${record.price}`,
    },
    {
      title: '有效期',
      dataIndex: 'validDays',
      hideInSearch: true,
      width: 100,
      render: (_, record) =>
        record.validDays ? `${record.validDays}天` : '无限期',
    },
    {
      title: '折扣率',
      dataIndex: 'discountRate',
      hideInSearch: true,
      width: 100,
      render: (_, record) =>
        record.discountRate
          ? `${formatNumber(record.discountRate * 10, 1)}折`
          : '-',
    },
    {
      title: '可用次数',
      dataIndex: 'usageLimit',
      hideInSearch: true,
      width: 100,
      render: (_, record) =>
        record.usageLimit && record.usageLimit >= 0
          ? `${record.usageLimit}次`
          : '不限',
    },
    {
      title: '发放数量',
      dataIndex: 'userCount',
      hideInSearch: true,
      width: 100,
      render: (_, record) => {
        // 这里假设API返回了userCount字段，如果没有，需要单独获取
        const count = record.userCount || 0;
        return <Tag color="blue">{count}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'isEnabled',
      width: 100,
      valueEnum: {
        true: { text: '启用', status: 'Success' },
        false: { text: '禁用', status: 'Error' },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button type="link" onClick={() => handleViewUsers(record)}>
            管理
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.MembershipCardType>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        search={{
          labelWidth: 'auto',
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
        }}
        scroll={{ x: 1200 }}
        request={async (params) => {
          const response = await index({
            ...params,
          });

          if (response.errCode) {
            message.error(response.msg || '获取权益卡类型列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }

          return {
            data: response.data?.list || [],
            success: true,
            total: response.data?.total || 0,
          };
        }}
      />

      {/* 用户列表抽屉 */}
      <Drawer
        title={`${currentCardName} - 发放用户列表`}
        width={800}
        placement="right"
        onClose={() => {
          setCurrentCardId(0);
          setCurrentCardName('');
          setDrawerVisible(false);
        }}
        open={drawerVisible}
        destroyOnClose={true}
      >
        <UserList cardId={currentCardId} />
      </Drawer>
    </>
  );
};

export default CardView;
